class ApiConstants {
  // Base URL for AllSportsAPI
  static const String baseUrl = 'https://allsportsapi.com/api';
  
  // API Key - Replace with your actual API key from allsportsapi.com
  static const String apiKey = 'YOUR_API_KEY_HERE';
  
  // Endpoints
  static const String countriesEndpoint = '/football/countries';
  static const String leaguesEndpoint = '/football/leagues';
  static const String teamsEndpoint = '/football/teams';
  static const String playersEndpoint = '/football/players';
  static const String topScorersEndpoint = '/football/topscorers';
  
  // Query parameters
  static const String apiKeyParam = 'APIkey';
  static const String countryIdParam = 'country_id';
  static const String leagueIdParam = 'league_id';
  static const String teamIdParam = 'team_id';
  static const String teamNameParam = 'team_name';
  
  // Default values
  static const int defaultTimeout = 30000; // 30 seconds
  static const int maxRetries = 3;
  
  // Error messages
  static const String networkError = 'Network error occurred';
  static const String timeoutError = 'Request timeout';
  static const String unknownError = 'Unknown error occurred';
  static const String noDataFound = 'No data found';
  static const String invalidApiKey = 'Invalid API key';
}
