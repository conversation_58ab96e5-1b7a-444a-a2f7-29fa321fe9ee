import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../constants/api_constants.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  late Dio _dio;

  void init() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConstants.baseUrl,
      connectTimeout: const Duration(milliseconds: ApiConstants.defaultTimeout),
      receiveTimeout: const Duration(milliseconds: ApiConstants.defaultTimeout),
      sendTimeout: const Duration(milliseconds: ApiConstants.defaultTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      requestHeader: true,
      responseHeader: false,
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add API key to all requests
        options.queryParameters[ApiConstants.apiKeyParam] = ApiConstants.apiKey;
        handler.next(options);
      },
      onError: (error, handler) async {
        // Handle network errors
        if (error.type == DioExceptionType.connectionTimeout ||
            error.type == DioExceptionType.receiveTimeout ||
            error.type == DioExceptionType.sendTimeout) {
          error = error.copyWith(message: ApiConstants.timeoutError);
        } else if (error.type == DioExceptionType.connectionError) {
          error = error.copyWith(message: ApiConstants.networkError);
        }
        handler.next(error);
      },
    ));
  }

  // Check internet connectivity
  Future<bool> _hasInternetConnection() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  // Generic GET request
  Future<Response> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    if (!await _hasInternetConnection()) {
      throw DioException(
        requestOptions: RequestOptions(path: endpoint),
        message: ApiConstants.networkError,
        type: DioExceptionType.connectionError,
      );
    }

    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
        options: options,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Generic POST request
  Future<Response> post(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    if (!await _hasInternetConnection()) {
      throw DioException(
        requestOptions: RequestOptions(path: endpoint),
        message: ApiConstants.networkError,
        type: DioExceptionType.connectionError,
      );
    }

    try {
      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Get countries
  Future<Response> getCountries() async {
    return await get(ApiConstants.countriesEndpoint);
  }

  // Get leagues by country
  Future<Response> getLeagues({required int countryId}) async {
    return await get(
      ApiConstants.leaguesEndpoint,
      queryParameters: {ApiConstants.countryIdParam: countryId},
    );
  }

  // Get teams by league
  Future<Response> getTeams({required int leagueId}) async {
    return await get(
      ApiConstants.teamsEndpoint,
      queryParameters: {ApiConstants.leagueIdParam: leagueId},
    );
  }

  // Search teams by name
  Future<Response> searchTeams({required String teamName}) async {
    return await get(
      ApiConstants.teamsEndpoint,
      queryParameters: {ApiConstants.teamNameParam: teamName},
    );
  }

  // Get players by team
  Future<Response> getPlayers({required int teamId}) async {
    return await get(
      ApiConstants.playersEndpoint,
      queryParameters: {ApiConstants.teamIdParam: teamId},
    );
  }

  // Search players by name
  Future<Response> searchPlayers({required String playerName}) async {
    return await get(
      ApiConstants.playersEndpoint,
      queryParameters: {'player_name': playerName},
    );
  }

  // Get top scorers by league
  Future<Response> getTopScorers({required int leagueId}) async {
    return await get(
      ApiConstants.topScorersEndpoint,
      queryParameters: {ApiConstants.leagueIdParam: leagueId},
    );
  }

  // Handle API response
  static Map<String, dynamic> handleResponse(Response response) {
    if (response.statusCode == 200) {
      return response.data as Map<String, dynamic>;
    } else {
      throw Exception('API Error: ${response.statusCode}');
    }
  }

  // Handle API errors
  static String handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.receiveTimeout:
        case DioExceptionType.sendTimeout:
          return ApiConstants.timeoutError;
        case DioExceptionType.connectionError:
          return ApiConstants.networkError;
        case DioExceptionType.badResponse:
          if (error.response?.statusCode == 401) {
            return ApiConstants.invalidApiKey;
          }
          return 'Server error: ${error.response?.statusCode}';
        default:
          return error.message ?? ApiConstants.unknownError;
      }
    }
    return ApiConstants.unknownError;
  }
}
