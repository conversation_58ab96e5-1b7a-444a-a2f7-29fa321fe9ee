import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import '../../core/constants/app_constants.dart';
import '../../core/models/league_model.dart';
import '../../core/services/api_service.dart';
import '../../core/routing/route_names.dart';
import '../../shared/widgets/app_drawer.dart';

class LeaguesScreen extends StatefulWidget {
  final int countryId;
  final String countryName;

  const LeaguesScreen({
    super.key,
    required this.countryId,
    required this.countryName,
  });

  @override
  State<LeaguesScreen> createState() => _LeaguesScreenState();
}

class _LeaguesScreenState extends State<LeaguesScreen> {
  final ApiService _apiService = ApiService();
  
  List<League> _leagues = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _apiService.init();
    _loadLeagues();
  }

  Future<void> _loadLeagues() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final response = await _apiService.getLeagues(countryId: widget.countryId);
      final data = ApiService.handleResponse(response);
      
      final List<dynamic> leaguesJson = data['result'] ?? [];
      final leagues = leaguesJson.map((json) => League.fromJson(json)).toList();
      
      setState(() {
        _leagues = leagues;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = ApiService.handleError(e);
        _isLoading = false;
      });
    }
  }

  void _onLeagueTapped(League league) {
    if (league.leagueId != null && league.leagueName != null) {
      context.go(RouteNames.teamsPath(league.leagueId!, league.leagueName!));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.countryName} Leagues'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      drawer: const AppDrawer(),
      body: _buildContent(),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return _buildLoadingList();
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_leagues.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.sports_soccer,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No leagues found for this country',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return _buildLeaguesList();
  }

  Widget _buildLoadingList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: 6,
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Card(
            margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: Container(
              height: 80,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                color: Colors.white,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            _error!,
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: _loadLeagues,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildLeaguesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _leagues.length,
      itemBuilder: (context, index) {
        final league = _leagues[index];
        return _buildLeagueCard(league);
      },
    );
  }

  Widget _buildLeagueCard(League league) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: InkWell(
        onTap: () => _onLeagueTapped(league),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Row(
            children: [
              // League Logo
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                  color: Colors.grey.shade100,
                ),
                child: league.leagueLogo?.isNotEmpty == true
                    ? CachedNetworkImage(
                        imageUrl: league.leagueLogo!,
                        fit: BoxFit.contain,
                        placeholder: (context, url) => const Center(
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        errorWidget: (context, url, error) => const Icon(
                          Icons.sports_soccer,
                          size: 30,
                          color: Colors.grey,
                        ),
                      )
                    : const Icon(
                        Icons.sports_soccer,
                        size: 30,
                        color: Colors.grey,
                      ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              
              // League Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // League Name
                    Text(
                      league.leagueName ?? 'Unknown League',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    
                    // League Year
                    if (league.leagueYear?.isNotEmpty == true) ...[
                      Text(
                        'Season: ${league.leagueYear}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                    
                    // Country Name
                    Text(
                      league.countryName ?? widget.countryName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Arrow Icon
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
