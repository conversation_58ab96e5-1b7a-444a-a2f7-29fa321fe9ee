class AppConstants {
  // App Information
  static const String appName = 'Sports App';
  static const String appVersion = '1.0.0';
  
  // Timing Constants
  static const int splashDuration = 2000; // 2 seconds
  static const int onboardingPageDuration = 3000; // 3 seconds
  static const int otpVerificationDelay = 3000; // 3 seconds
  
  // SharedPreferences Keys
  static const String keyIsFirstTime = 'is_first_time';
  static const String keyIsLoggedIn = 'is_logged_in';
  static const String keyUserData = 'user_data';
  static const String keyLoginType = 'login_type';
  static const String keyPhoneNumber = 'phone_number';
  static const String keyFirstName = 'first_name';
  static const String keyLastName = 'last_name';
  static const String keyCurrentOtp = 'current_otp';
  
  // Login Types
  static const String loginTypeGoogle = 'google';
  static const String loginTypeOtp = 'otp';
  
  // Sports Types
  static const List<String> sportsTypes = [
    'Football',
    'Basketball',
    'Cricket',
    'Tennis',
  ];
  
  // Sports Icons (using asset paths)
  static const Map<String, String> sportsIcons = {
    'Football': 'assets/images/football.png',
    'Basketball': 'assets/images/basketball.png',
    'Cricket': 'assets/images/cricket.png',
    'Tennis': 'assets/images/tennis.png',
  };
  
  // Default Images
  static const String defaultPlayerImage = 'assets/images/default_player.png';
  static const String defaultTeamImage = 'assets/images/default_team.png';
  static const String defaultCountryImage = 'assets/images/default_country.png';
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  
  // Grid Constants
  static const int homeGridCrossAxisCount = 2;
  static const int countriesGridCrossAxisCount = 2;
  static const int teamsGridCrossAxisCount = 2;
  
  // Animation Durations
  static const int shortAnimationDuration = 300;
  static const int mediumAnimationDuration = 500;
  static const int longAnimationDuration = 800;
  
  // Error Messages
  static const String genericError = 'Something went wrong. Please try again.';
  static const String networkError = 'No internet connection. Please check your network.';
  static const String invalidOtp = 'Invalid OTP. Please try again.';
  static const String invalidPhoneNumber = 'Please enter a valid phone number.';
  static const String emptyOtp = 'Please enter the OTP.';
  static const String emptyPhoneNumber = 'Please enter your phone number.';
  static const String locationPermissionDenied = 'Location permission denied.';
  static const String locationServiceDisabled = 'Location service is disabled.';
  
  // Success Messages
  static const String loginSuccess = 'Login successful!';
  static const String otpGenerated = 'OTP generated successfully!';
  static const String locationFetched = 'Location fetched successfully!';
  
  // Coming Soon Message
  static const String comingSoonMessage = 'Coming soon';
  
  // Share Messages
  static const String sharePlayerMessage = 'Check out this player: ';
}
