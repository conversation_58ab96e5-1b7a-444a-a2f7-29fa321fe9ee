import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';

class SharedPreferencesHelper {
  static SharedPreferences? _preferences;

  static Future<void> init() async {
    _preferences = await SharedPreferences.getInstance();
  }

  static SharedPreferences get preferences {
    if (_preferences == null) {
      throw Exception('SharedPreferences not initialized. Call init() first.');
    }
    return _preferences!;
  }

  // First time app launch
  static bool get isFirstTime {
    return preferences.getBool(AppConstants.keyIsFirstTime) ?? true;
  }

  static Future<void> setFirstTime(bool value) async {
    await preferences.setBool(AppConstants.keyIsFirstTime, value);
  }

  // Login status
  static bool get isLoggedIn {
    return preferences.getBool(AppConstants.keyIsLoggedIn) ?? false;
  }

  static Future<void> setLoggedIn(bool value) async {
    await preferences.setBool(AppConstants.keyIsLoggedIn, value);
  }

  // Login type
  static String get loginType {
    return preferences.getString(AppConstants.keyLoginType) ?? '';
  }

  static Future<void> setLoginType(String value) async {
    await preferences.setString(AppConstants.keyLoginType, value);
  }

  // Phone number
  static String get phoneNumber {
    return preferences.getString(AppConstants.keyPhoneNumber) ?? '';
  }

  static Future<void> setPhoneNumber(String value) async {
    await preferences.setString(AppConstants.keyPhoneNumber, value);
  }

  // First name
  static String get firstName {
    return preferences.getString(AppConstants.keyFirstName) ?? '';
  }

  static Future<void> setFirstName(String value) async {
    await preferences.setString(AppConstants.keyFirstName, value);
  }

  // Last name
  static String get lastName {
    return preferences.getString(AppConstants.keyLastName) ?? '';
  }

  static Future<void> setLastName(String value) async {
    await preferences.setString(AppConstants.keyLastName, value);
  }

  // Current OTP
  static String get currentOtp {
    return preferences.getString(AppConstants.keyCurrentOtp) ?? '';
  }

  static Future<void> setCurrentOtp(String value) async {
    await preferences.setString(AppConstants.keyCurrentOtp, value);
  }

  // User data as JSON
  static Map<String, dynamic> get userData {
    final String userDataString = preferences.getString(AppConstants.keyUserData) ?? '{}';
    try {
      return json.decode(userDataString) as Map<String, dynamic>;
    } catch (e) {
      return {};
    }
  }

  static Future<void> setUserData(Map<String, dynamic> value) async {
    await preferences.setString(AppConstants.keyUserData, json.encode(value));
  }

  // Clear all data (logout)
  static Future<void> clearAll() async {
    await preferences.clear();
    // Keep the first time flag as false since user has already seen onboarding
    await setFirstTime(false);
  }

  // Clear user session data only
  static Future<void> clearUserSession() async {
    await preferences.remove(AppConstants.keyIsLoggedIn);
    await preferences.remove(AppConstants.keyLoginType);
    await preferences.remove(AppConstants.keyPhoneNumber);
    await preferences.remove(AppConstants.keyFirstName);
    await preferences.remove(AppConstants.keyLastName);
    await preferences.remove(AppConstants.keyUserData);
    await preferences.remove(AppConstants.keyCurrentOtp);
  }

  // Helper method to save Google login data
  static Future<void> saveGoogleLoginData({
    required String firstName,
    required String lastName,
  }) async {
    await setLoggedIn(true);
    await setLoginType(AppConstants.loginTypeGoogle);
    await setFirstName(firstName);
    await setLastName(lastName);
    await setUserData({
      'firstName': firstName,
      'lastName': lastName,
      'loginType': AppConstants.loginTypeGoogle,
    });
  }

  // Helper method to save OTP login data
  static Future<void> saveOtpLoginData({
    required String phoneNumber,
  }) async {
    await setLoggedIn(true);
    await setLoginType(AppConstants.loginTypeOtp);
    await setPhoneNumber(phoneNumber);
    await setUserData({
      'phoneNumber': phoneNumber,
      'loginType': AppConstants.loginTypeOtp,
    });
  }
}
